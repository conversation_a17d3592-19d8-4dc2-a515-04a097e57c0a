import {
  EnvironmentFilled,
  MenuFoldOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { Input } from "antd";
import { Scrollbars } from "react-custom-scrollbars";
import chinaTreeData from "@pansy/china-division";
import { useMemo } from "react";
import CustomTree from "./CustomTree";
import useStore from "@/store";
import { streetData } from "./mock";

function ProvinceList() {
  const selectCode = useStore((state) => state.selectCode);

  const onSetSelectCode = (code: number) => {
    useStore.setState((draft) => {
      draft.selectCode = code;
      // alert(code)
    });
  };

  // 返回全国地图
  const onReturnChina = () => {
    useStore.setState((draft) => {
      draft.selectCode = 100000;
    });
  };

  // 省市树形结构数据
  const provinceTreeData = useMemo(() => {
    return chinaTreeData.map((item) => {
      const firstItem = item.children?.[0];
      // 判断是否是直辖市
      const isMunicipality = firstItem?.label === "市辖区";
      // 直辖市展示区
      const children = isMunicipality ? firstItem.children : item.children;
      return {
        title: item.label,
        key: item.value,
        children: children?.map((child) => {
          const cityData = streetData.find(
            (cItem) => cItem.code === item.value
          );
          const areaData = cityData?.children?.find(
            (aItem) => aItem.code === child.value
          );
          return {
            title: child.label,
            key: child.value,
            children: areaData?.children.map((aItem) => {
              return {
                title: aItem.name,
                key: aItem.code,
              };
            }) ?? [],
          };
        }),
      };
    });
  }, []);

  return (
    <div className="bg-[#2A4160] absolute top-[280px] left-[1536px] w-[468px] h-[1794px] rounded-[12px] overflow-hidden border-2 border-[rgba(95,234,255,.65)]">
      <div className="h-[84px] px-[36px] space-x-[24px] flex items-center bg-[rgba(79,101,133,0.85)] relative">
        <EnvironmentFilled className="text-[30px] text-[rgba(255,255,255,0.65)]! relative top-[-2px]" />
        <span className="text-[42px] text-[rgba(255,255,255,.8)]">
          当前位置
        </span>
        <span
          onClick={onReturnChina}
          className="border-1 h-[54px] cursor-pointer border-[rgba(255,255,255,0.65)] rounded-[12px] text-[rgba(255,255,255,0.85)] text-[26px] leading-[54px] absolute right-[24px] top-[24px] px-[24px] y-centered"
        >
          返回
        </span>
      </div>
      <div className="h-[116px] flex items-center px-6 space-x-[24px]">
        <Input
          className="h-[64px]! rounded-[8px]! overflow-hidden text-[28px]! pl-[30px]!"
          placeholder="请输入"
          prefix={<SearchOutlined className="text-[26px]! mr-[24px]" />}
        />
        <MenuFoldOutlined className="rotate-180 text-[48px] text-[#A2AAB5]! cursor-pointer" />
      </div>
      <div className="absolute bottom-0 pb-3 top-[200px] w-full backdrop-blur-[30px]">
        <Scrollbars>
          <CustomTree
            treeData={provinceTreeData}
            onSelect={(value) => {
              if (value) {
                onSetSelectCode(Number(value));
              }
            }}
            selectedKey={selectCode}
          />
        </Scrollbars>
      </div>
    </div>
  );
}
export default ProvinceList;
